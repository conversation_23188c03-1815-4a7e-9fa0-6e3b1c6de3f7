"""
Legacy SQL functions - now using the enhanced SQLAgent class.
This module is kept for backward compatibility.
"""

from ..agent import SQLAgent

# Create a global instance for backward compatibility
_sql_agent_instance = None

def get_sql_agent():
    """Get the SQL agent instance."""
    global _sql_agent_instance
    if _sql_agent_instance is None:
        _sql_agent_instance = SQLAgent()
    return _sql_agent_instance

# Legacy interface
agent = get_sql_agent().agent
model = get_sql_agent().model
db = get_sql_agent().db



