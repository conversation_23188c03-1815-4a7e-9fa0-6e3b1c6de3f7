"""
Agent Supervisor for coordinating between SQL and RAG agents.
"""

import logging
from typing import Dict, Any, List, Literal

from langchain.chat_models import init_chat_model
from langchain.schema import HumanMessage, SystemMessage
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .state import Agent<PERSON><PERSON>, create_initial_state
from sql_agent.agent import SQLAgent
from rag_agent.agent import RAGAgent

logger = logging.getLogger(__name__)


class AgentSupervisor:
    """
    Supervisor agent that coordinates between SQL and RAG agents.
    
    The supervisor:
    1. Analyzes user queries to determine the best agent
    2. Routes queries to appropriate agents
    3. Combines results when needed
    4. Provides final responses to users
    """
    
    def __init__(self):
        """Initialize the Agent Supervisor."""
        self.model = None
        self.sql_agent = None
        self.rag_agent = None
        self.graph = None
        self._initialize_supervisor()
    
    def _initialize_supervisor(self):
        """Initialize the supervisor and all sub-agents."""
        try:
            # Initialize language model
            self.model = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
            
            # Initialize agents
            self.sql_agent = SQLAgent()
            self.rag_agent = RAGAgent()
            
            # Build the workflow graph
            self._build_graph()
            
            logger.info("Agent Supervisor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Agent Supervisor: {e}")
            raise
    
    def _build_graph(self):
        """Build the LangGraph workflow for agent coordination."""
        # Create the state graph
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("supervisor", self._supervisor_node)
        workflow.add_node("sql_agent", self._sql_agent_node)
        workflow.add_node("rag_agent", self._rag_agent_node)
        workflow.add_node("combiner", self._combiner_node)
        
        # Add edges
        workflow.set_entry_point("supervisor")
        
        # Conditional routing from supervisor
        workflow.add_conditional_edges(
            "supervisor",
            self._route_query,
            {
                "sql": "sql_agent",
                "rag": "rag_agent",
                "both": "sql_agent",  # Start with SQL, then go to RAG
                "end": END
            }
        )
        
        # From SQL agent
        workflow.add_conditional_edges(
            "sql_agent",
            self._after_sql_agent,
            {
                "rag": "rag_agent",
                "combiner": "combiner",
                "end": END
            }
        )
        
        # From RAG agent
        workflow.add_conditional_edges(
            "rag_agent",
            self._after_rag_agent,
            {
                "combiner": "combiner",
                "end": END
            }
        )
        
        # From combiner
        workflow.add_edge("combiner", END)
        
        # Compile the graph
        memory = MemorySaver()
        self.graph = workflow.compile(checkpointer=memory)
    
    def _supervisor_node(self, state: AgentState) -> AgentState:
        """
        Supervisor node that analyzes the query and decides routing.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated state with routing decision
        """
        try:
            query = state["user_query"]
            logger.info(f"Supervisor analyzing query: {query}")
            
            # Analyze query to determine routing
            routing_decision = self._analyze_query(query)
            
            state["route_to"] = routing_decision
            state["current_agent"] = "supervisor"
            
            # Add supervisor message
            state["messages"].append({
                "role": "supervisor",
                "content": f"Analyzing query and routing to: {routing_decision}",
                "agent": "supervisor"
            })
            
            return state
            
        except Exception as e:
            logger.error(f"Error in supervisor node: {e}")
            state["route_to"] = "end"
            state["final_response"] = f"Error in query analysis: {str(e)}"
            return state
    
    def _sql_agent_node(self, state: AgentState) -> AgentState:
        """
        SQL agent node for database queries.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated state with SQL results
        """
        try:
            query = state["user_query"]
            logger.info(f"SQL agent processing query: {query}")
            
            # Query SQL agent
            sql_result = self.sql_agent.query(query)
            
            state["sql_results"] = sql_result
            state["current_agent"] = "sql"
            
            # Add SQL agent message
            state["messages"].append({
                "role": "assistant",
                "content": sql_result.get("response", "No response from SQL agent"),
                "agent": "sql",
                "success": sql_result.get("success", False)
            })
            
            return state
            
        except Exception as e:
            logger.error(f"Error in SQL agent node: {e}")
            state["sql_results"] = {
                "success": False,
                "error": str(e),
                "response": f"SQL agent error: {str(e)}"
            }
            return state
    
    def _rag_agent_node(self, state: AgentState) -> AgentState:
        """
        RAG agent node for semantic search.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated state with RAG results
        """
        try:
            query = state["user_query"]
            logger.info(f"RAG agent processing query: {query}")
            
            # Query RAG agent
            rag_result = self.rag_agent.query(query)
            
            state["rag_results"] = rag_result
            state["current_agent"] = "rag"
            
            # Add RAG agent message
            state["messages"].append({
                "role": "assistant",
                "content": rag_result.get("response", "No response from RAG agent"),
                "agent": "rag",
                "success": rag_result.get("success", False)
            })
            
            return state
            
        except Exception as e:
            logger.error(f"Error in RAG agent node: {e}")
            state["rag_results"] = {
                "success": False,
                "error": str(e),
                "response": f"RAG agent error: {str(e)}"
            }
            return state
    
    def _combiner_node(self, state: AgentState) -> AgentState:
        """
        Combiner node that synthesizes results from multiple agents.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated state with combined response
        """
        try:
            logger.info("Combining results from multiple agents")
            
            sql_results = state.get("sql_results")
            rag_results = state.get("rag_results")
            
            # Generate combined response
            combined_response = self._generate_combined_response(
                state["user_query"], sql_results, rag_results
            )
            
            state["final_response"] = combined_response
            state["current_agent"] = "combiner"
            
            # Add combiner message
            state["messages"].append({
                "role": "assistant",
                "content": combined_response,
                "agent": "combiner"
            })
            
            return state
            
        except Exception as e:
            logger.error(f"Error in combiner node: {e}")
            state["final_response"] = f"Error combining results: {str(e)}"
            return state
    
    def _analyze_query(self, query: str) -> Literal["sql", "rag", "both", "end"]:
        """
        Analyze user query to determine routing strategy.
        
        Args:
            query: User's query
            
        Returns:
            Routing decision
        """
        try:
            # Check if SQL agent thinks it can handle the query
            sql_relevant = self.sql_agent.is_sql_query(query)
            
            # Check if RAG agent thinks it can handle the query
            rag_relevant = self.rag_agent.is_rag_query(query)
            
            # Decision logic
            if sql_relevant and rag_relevant:
                return "both"
            elif sql_relevant:
                return "sql"
            elif rag_relevant:
                return "rag"
            else:
                # Default to RAG for general queries
                return "rag"
                
        except Exception as e:
            logger.error(f"Error analyzing query: {e}")
            return "rag"  # Default fallback
    
    def _route_query(self, state: AgentState) -> str:
        """Route query based on supervisor decision."""
        return state.get("route_to", "end")
    
    def _after_sql_agent(self, state: AgentState) -> str:
        """Decide what to do after SQL agent."""
        route_to = state.get("route_to")
        
        if route_to == "both":
            return "rag"
        elif state.get("sql_results", {}).get("success"):
            return "end"
        else:
            return "rag"  # Try RAG if SQL failed
    
    def _after_rag_agent(self, state: AgentState) -> str:
        """Decide what to do after RAG agent."""
        route_to = state.get("route_to")
        
        if route_to == "both" and state.get("sql_results"):
            return "combiner"
        else:
            return "end"
    
    def _generate_combined_response(self, query: str, sql_results: Dict[str, Any], 
                                  rag_results: Dict[str, Any]) -> str:
        """Generate a combined response from both agents."""
        try:
            system_prompt = """
You are a response synthesizer that combines information from SQL database queries and semantic article search.

Your task is to create a comprehensive, coherent response that:
1. Integrates structured data from SQL queries with contextual information from articles
2. Provides a complete answer to the user's question
3. Maintains clarity and readability
4. Cites sources appropriately

Format your response to be helpful and informative, combining the best of both data sources.
            """.strip()
            
            user_prompt = f"""
User Question: {query}

SQL Results:
{sql_results.get('response', 'No SQL results') if sql_results else 'No SQL results'}

Article Search Results:
{rag_results.get('response', 'No article results') if rag_results else 'No article results'}

Please provide a comprehensive response that combines these information sources.
            """.strip()
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.model.invoke(messages)
            
            if hasattr(response, 'content'):
                return response.content
            elif isinstance(response, dict) and 'content' in response:
                return response['content']
            else:
                return str(response)
                
        except Exception as e:
            logger.error(f"Error generating combined response: {e}")
            return f"I found information from both database and articles, but encountered an error combining them: {str(e)}"
    
    def process_query(self, user_query: str, thread_id: str = "default") -> Dict[str, Any]:
        """
        Process a user query through the agent system.
        
        Args:
            user_query: The user's question
            thread_id: Thread ID for conversation tracking
            
        Returns:
            Dictionary containing the response and metadata
        """
        try:
            logger.info(f"Processing query: {user_query}")
            
            # Create initial state
            initial_state = create_initial_state(user_query)
            
            # Run the graph
            config = {"configurable": {"thread_id": thread_id}}
            final_state = self.graph.invoke(initial_state, config)
            
            # Extract final response
            final_response = final_state.get("final_response")
            if not final_response:
                # Get the last assistant message
                messages = final_state.get("messages", [])
                for message in reversed(messages):
                    if message.get("role") == "assistant":
                        final_response = message.get("content")
                        break
            
            return {
                "success": True,
                "response": final_response or "I couldn't generate a response to your query.",
                "sql_results": final_state.get("sql_results"),
                "rag_results": final_state.get("rag_results"),
                "route_taken": final_state.get("route_to"),
                "messages": final_state.get("messages", [])
            }
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": f"I encountered an error processing your query: {str(e)}"
            }
