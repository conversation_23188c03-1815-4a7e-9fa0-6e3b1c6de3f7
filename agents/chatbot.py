"""
Main chatbot interface that uses the agent supervisor.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from .supervisor import AgentSupervisor

logger = logging.getLogger(__name__)


class EventsArticlesChatbot:
    """
    Main chatbot interface for the Events Articles system.
    
    This chatbot:
    - Provides a simple interface for users to ask questions
    - Uses the agent supervisor to coordinate responses
    - Maintains conversation history
    - Handles different types of queries (SQL, RAG, combined)
    """
    
    def __init__(self):
        """Initialize the chatbot."""
        self.supervisor = None
        self.conversation_history = {}
        self._initialize_chatbot()
    
    def _initialize_chatbot(self):
        """Initialize the chatbot with agent supervisor."""
        try:
            self.supervisor = AgentSupervisor()
            logger.info("Events Articles Chatbot initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize chatbot: {e}")
            raise
    
    def chat(self, message: str, user_id: str = "default", 
             conversation_id: str = None) -> Dict[str, Any]:
        """
        Process a chat message and return response.
        
        Args:
            message: User's message/question
            user_id: Unique identifier for the user
            conversation_id: Optional conversation ID for threading
            
        Returns:
            Dictionary containing response and metadata
        """
        try:
            # Generate conversation ID if not provided
            if not conversation_id:
                conversation_id = f"{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            logger.info(f"Processing chat message from user {user_id}: {message}")
            
            # Process the query through the supervisor
            result = self.supervisor.process_query(message, conversation_id)
            
            # Store conversation history
            self._update_conversation_history(
                user_id, conversation_id, message, result
            )
            
            # Format response for chat interface
            chat_response = {
                "response": result.get("response", "I couldn't process your request."),
                "success": result.get("success", False),
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "route_taken": result.get("route_taken"),
                    "agents_used": self._extract_agents_used(result),
                    "sql_results_available": bool(result.get("sql_results")),
                    "rag_results_available": bool(result.get("rag_results"))
                }
            }
            
            # Add error information if present
            if not result.get("success") and result.get("error"):
                chat_response["error"] = result["error"]
            
            return chat_response
            
        except Exception as e:
            logger.error(f"Error in chat processing: {e}")
            return {
                "response": f"I encountered an error processing your message: {str(e)}",
                "success": False,
                "conversation_id": conversation_id or "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def get_conversation_history(self, user_id: str, 
                               conversation_id: str = None) -> List[Dict[str, Any]]:
        """
        Get conversation history for a user.
        
        Args:
            user_id: User identifier
            conversation_id: Optional specific conversation ID
            
        Returns:
            List of conversation messages
        """
        try:
            user_history = self.conversation_history.get(user_id, {})
            
            if conversation_id:
                return user_history.get(conversation_id, [])
            else:
                # Return all conversations for the user
                all_messages = []
                for conv_id, messages in user_history.items():
                    all_messages.extend(messages)
                return sorted(all_messages, key=lambda x: x.get("timestamp", ""))
                
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    def clear_conversation_history(self, user_id: str, 
                                 conversation_id: str = None) -> bool:
        """
        Clear conversation history.
        
        Args:
            user_id: User identifier
            conversation_id: Optional specific conversation ID to clear
            
        Returns:
            True if successful
        """
        try:
            if user_id not in self.conversation_history:
                return True
            
            if conversation_id:
                # Clear specific conversation
                if conversation_id in self.conversation_history[user_id]:
                    del self.conversation_history[user_id][conversation_id]
            else:
                # Clear all conversations for user
                del self.conversation_history[user_id]
            
            logger.info(f"Cleared conversation history for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing conversation history: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        Get system status and health information.
        
        Returns:
            Dictionary containing system status
        """
        try:
            status = {
                "chatbot_status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "components": {}
            }
            
            # Check supervisor
            if self.supervisor:
                status["components"]["supervisor"] = "initialized"
                
                # Check SQL agent
                try:
                    if self.supervisor.sql_agent:
                        db_info = self.supervisor.sql_agent.get_database_info()
                        if "error" not in db_info:
                            status["components"]["sql_agent"] = "healthy"
                            status["components"]["database_tables"] = db_info.get("tables", [])
                        else:
                            status["components"]["sql_agent"] = f"error: {db_info['error']}"
                    else:
                        status["components"]["sql_agent"] = "not_initialized"
                except Exception as e:
                    status["components"]["sql_agent"] = f"error: {str(e)}"
                
                # Check RAG agent
                try:
                    if self.supervisor.rag_agent and self.supervisor.rag_agent.vector_store:
                        status["components"]["rag_agent"] = "healthy"
                        status["components"]["vector_store"] = "connected"
                    else:
                        status["components"]["rag_agent"] = "not_initialized"
                except Exception as e:
                    status["components"]["rag_agent"] = f"error: {str(e)}"
            else:
                status["chatbot_status"] = "error"
                status["components"]["supervisor"] = "not_initialized"
            
            # Add conversation statistics
            status["statistics"] = {
                "total_users": len(self.conversation_history),
                "total_conversations": sum(
                    len(convs) for convs in self.conversation_history.values()
                )
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                "chatbot_status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def _update_conversation_history(self, user_id: str, conversation_id: str,
                                   message: str, result: Dict[str, Any]):
        """Update conversation history with new message and response."""
        try:
            if user_id not in self.conversation_history:
                self.conversation_history[user_id] = {}
            
            if conversation_id not in self.conversation_history[user_id]:
                self.conversation_history[user_id][conversation_id] = []
            
            # Add user message
            self.conversation_history[user_id][conversation_id].append({
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat()
            })
            
            # Add assistant response
            self.conversation_history[user_id][conversation_id].append({
                "role": "assistant",
                "content": result.get("response", "No response"),
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "success": result.get("success", False),
                    "route_taken": result.get("route_taken"),
                    "agents_used": self._extract_agents_used(result)
                }
            })
            
        except Exception as e:
            logger.error(f"Error updating conversation history: {e}")
    
    def _extract_agents_used(self, result: Dict[str, Any]) -> List[str]:
        """Extract which agents were used from the result."""
        agents_used = []
        
        if result.get("sql_results"):
            agents_used.append("sql")
        
        if result.get("rag_results"):
            agents_used.append("rag")
        
        # Check messages for agent information
        messages = result.get("messages", [])
        for message in messages:
            agent = message.get("agent")
            if agent and agent not in agents_used:
                agents_used.append(agent)
        
        return agents_used
