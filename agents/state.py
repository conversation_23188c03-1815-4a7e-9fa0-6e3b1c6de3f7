"""
Shared state management for the agent system.
"""

from typing import Annotated, Dict, List, Optional, Any
from typing_extensions import TypedDict
from langgraph.graph.message import add_messages


class AgentState(TypedDict):
    """
    Shared state between all agents in the system.
    
    This state is passed between agents and contains:
    - messages: Conversation history
    - current_agent: Which agent is currently active
    - sql_results: Results from SQL queries
    - rag_results: Results from RAG searches
    - user_query: Original user query
    - context: Additional context information
    - next_action: What the supervisor should do next
    """
    
    # Conversation messages
    messages: Annotated[List[Dict[str, Any]], add_messages]
    
    # Current active agent
    current_agent: Optional[str]
    
    # User's original query
    user_query: str
    
    # Results from different agents
    sql_results: Optional[Dict[str, Any]]
    rag_results: Optional[Dict[str, Any]]
    
    # Additional context
    context: Dict[str, Any]
    
    # Next action for supervisor
    next_action: Optional[str]
    
    # Agent routing decision
    route_to: Optional[str]
    
    # Final response
    final_response: Optional[str]


def create_initial_state(user_query: str) -> AgentState:
    """
    Create initial state for a new conversation.
    
    Args:
        user_query: The user's query to process
        
    Returns:
        Initial AgentState
    """
    return AgentState(
        messages=[],
        current_agent=None,
        user_query=user_query,
        sql_results=None,
        rag_results=None,
        context={},
        next_action=None,
        route_to=None,
        final_response=None
    )
